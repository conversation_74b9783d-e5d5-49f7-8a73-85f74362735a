<html>

<head>
  <title>infinityRP</title>
  <meta charset="utf-8" />
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <meta name="mobile-web-app-capable" content="yes" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=500,  user-scalable=no" />
  <link href="static/mdi_font/css/materialdesignicons.min.css" rel="stylesheet" />

  <link href="static/vuetify.min.css" rel="stylesheet" />
  <link href="static/highlight.min.css" rel="stylesheet" />
  <link href="common.css" rel="stylesheet" />
  <script src="static/vue.min.js"></script>
  <script src="static/vuetify.min.js"></script>
  <script src="static/markdown-it.min.js"></script>
  <script src="static/highlight.min.js"></script>
  <style>
    .v-navigation-drawer__content::-webkit-scrollbar {
      width: 5px;
    }

    .v-navigation-drawer__content::-webkit-scrollbar-track {
      background-color: #333;
    }

    /* 滚动条的滑轨背景颜色 */

    .v-navigation-drawer__content::-webkit-scrollbar-thumb {
      background-color: #aaa;
    }

    /* 滑块颜色 */

    .v-navigation-drawer__content::-webkit-scrollbar-button {
      display: none;
    }

    /* 滑轨两头的监听按钮颜色 */
    .answer th,
    .answer td {
      outline: 1px solid;
      padding: 2px;
    }

    div[aria-haspopup] {
      display: inline-block;
      margin: 10px;
    }

    .answer table {
      outline: 1px solid;
    }

    .ask,
    .answer {
      padding: 1em;
      background: #fff;
      border-radius: 16px;
      max-width: 80%;
      margin: 0 10px;
      word-break: break-word;
      line-height: 1.2;
    }


    .ask {
      margin-left: auto;
      white-space: break-spaces;
    }

    .头像 {
      margin-bottom: auto;
      color: #fff !important;
      position: unset;
    }

    .answer img {
      max-width: 100%;
    }

    #app,
    .v-application--wrap {
      background: transparent;
      min-height: 100%;
      /* background: #cecece0a; */
    }

    .float {
      position: absolute !important;
    }

    .float button {
      display: block !important;
    }

    header {
      position: fixed !important;
      top: 0;
      z-index: 1;
      width: 100%;
    }

    html,
    .v-window__container,
    .chatContent {
      background-color: #f0f0f0;
    }


    .v-window-item {
      min-height: 100%;
      margin: 3%;
    }

    .v-window-item:has(iframe) {
      margin: 0;
    }

    .user-avatar {
      background-color: #0000a0bb;
    }

    .bot-avatar {
      background-color: #2196f3;
    }

    .v-application p {
      margin-bottom: 10px;
      white-space: break-spaces;
    }

    .v-application a {
      line-height: 1.3;
    }

    .v-input.v-textarea.v-text-field {
      padding-top: 0;
      margin-top: 0;
    }

    .v-application--is-ltr .v-input--selection-controls__input {
      /* 紧凑右上角开关 */
      margin-right: 0px;
    }

    .v-slide-group:not(.v-slide-group--has-affixes)>.v-slide-group__prev {
      /* 隐藏手机选项卡左侧空白 */
      display: none !important;
    }

    .v-sheet.v-card {
      margin: 20px;
      padding: 10px;
    }

    .chat_toolbar {
      color: #d0d0d0;
    }

    .chat_toolbar .v-input,
    .chat_toolbar div[aria-haspopup] {
      margin: 0;
    }
  </style>
</head>

<body>
  <div id="app" v-cloak>
    <v-app>
      <v-navigation-drawer width="350" v-model="drawer" style="height: 100%; position: fixed" temporary>
        <v-card elevation="2">
          <v-card-title>模型运行参数</v-card-title>
          <v-divider></v-divider>
          <v-card-text><br />
            <v-form>

              <v-tooltip bottom>
                <template v-slot:activator="{ on, attrs }">
                  <div v-bind="attrs" v-on="on" style="width: 100%;">
                    <v-slider :min="2000" :max="50000" :step="100" v-model="max_length" :thumb-color="color"
                      label="max_length" thumb-label><template v-slot:append>
                        <v-text-field v-model="max_length" class="mt-0 pt-0" type="number" style="width: 60px">
                        </v-text-field>
                      </template>
                    </v-slider>
                  </div>
                </template>
                <span>最大生成token数</span>
              </v-tooltip>

              <v-tooltip bottom>
                <template v-slot:activator="{ on, attrs }">
                  <div v-bind="attrs" v-on="on" style="width: 100%;">
                    <v-slider :min="0" :max="2" :step="0.1" v-model="temperature" :thumb-color="color"
                      label="temperature" thumb-label><template v-slot:append>
                        <v-text-field v-model="temperature" class="mt-0 pt-0" type="number" style="width: 60px">
                        </v-text-field>
                      </template>
                    </v-slider>
                  </div>
                </template>
                <span>温度（随机性）</span>
              </v-tooltip>

              <v-tooltip bottom>
                <template v-slot:activator="{ on, attrs }">
                  <div v-bind="attrs" v-on="on" style="width: 100%;">
                    <v-slider :min="0" :max="1" :step="0.1" v-model="top_p" :thumb-color="color" label="top_p"
                      thumb-label>
                      <template v-slot:append>
                        <v-text-field v-model="top_p" class="mt-0 pt-0" type="number" style="width: 60px">
                        </v-text-field>
                      </template>
                    </v-slider>
                  </div>
                </template>
                <span>选取的前p个输出数量</span>
              </v-tooltip>
            </v-form>
          </v-card-text>
        </v-card>
      </v-navigation-drawer>
      <v-toolbar v-if="show_header" style="opacity: 80%">
        <v-app-bar-nav-icon @click.stop="drawer = !drawer"></v-app-bar-nav-icon>
        <v-toolbar-title style="font-size: xx-large; font-weight: bolder;color: black;">infinityRP</v-toolbar-title>
        <v-spacer></v-spacer>
        <div class="chat_toolbar">
          <b style="
          width: 5em;
          display: inline-block;
      ">TPS:{{TPS.toFixed(2)}}</b>
          <v-scroll-x-transition>
            <v-chip v-if="func_mode.name!=''" :color="color" outlined close @click:close="current_func=''">
              <span class="d-inline-block text-truncate" style="max-width: 120px">
                {{func_mode.name}}
              </span>
            </v-chip>
          </v-scroll-x-transition>
          <v-tooltip bottom v-for="button in buttons">
            <template v-slot:activator="{ on, attrs }">
              <div v-bind="attrs" v-on="on">
                <v-icon :color="button.color()" text @click="button.click" large>
                  mdi-{{button.icon}}
                </v-icon>
              </div>
            </template>
            <span>{{button.description}}</span>
          </v-tooltip>
          {{buttons.length>0?"|":""}}
          <v-tooltip bottom v-if="(!loading)&&(chat.length>0)">
            <template v-slot:activator="{ on, attrs }">
              <v-icon :color="'red'" v-bind="attrs" v-on="on"
                @click="send(kill_prompt, '死亡惩罚', show = true, sources = [],addition_args = { })" large>
                mdi-alert
              </v-icon>
            </template>
            <span>惩罚</span>
          </v-tooltip>
          <v-tooltip bottom v-if="(!loading)&&(chat.length>0)">
            <template v-slot:activator="{ on, attrs }">
              <v-icon :color="color" v-bind="attrs" v-on="on" @click="re_generate()" large>
                mdi-refresh
              </v-icon>
            </template>
            <span>重试</span>
          </v-tooltip>
          <v-tooltip bottom v-if="(!loading)&&(chat.length>0)">
            <template v-slot:activator="{ on, attrs }">
              <v-icon :color="color" v-bind="attrs" v-on="on" @click="copyChatContentToClipboard()" large>
                mdi-file-png-box
              </v-icon>
            </template>
            <span>截图</span>
          </v-tooltip>
          <v-tooltip bottom>
            <template v-slot:activator="{ on, attrs }">
              <div v-bind="attrs" v-on="on">
                <v-icon :color="color" text @click="chat=JSON.parse( JSON.stringify(nn_history));save_history()" large>
                  mdi-delete-clock-outline
                </v-icon>
              </div>
            </template>
            <span>清除历史</span>
          </v-tooltip>
        </div>
      </v-toolbar>
      <br />
      <br />
      <br />
      <div class="chatContent">
        <div v-for="(current_conversation, index) in chat" :key="index"
          :class="['d-flex flex-row align-center my-2', current_conversation.role == 'User' ? 'justify-end': null]">
          <span v-if="current_conversation.role == 'User'" class="ask"
            v-text="current_conversation.keyword||current_conversation.content"></span>
          <v-hover v-slot="{ hover }">
            <div style="margin-bottom: auto;">
              <v-avatar :class="current_conversation.role == 'User' ? 'user-avatar': 'bot-avatar'" size="36" class="头像">
                <span>{{ {User:"问",Assistant:'答'}[current_conversation.role]
                  }}</span>
              </v-avatar>
              <br />
              <v-expand-transition>
                <div v-if="hover" class="float">
                  <v-icon v-if="!loading" large text :color="color"
                    @click="delete_current_conversation(current_conversation)">
                    mdi-delete
                  </v-icon>

                  <v-icon large text :color="color" @click="copy(current_conversation.content)">
                    mdi-content-copy
                  </v-icon>
                  <v-icon large text :color="color" @click="edit(current_conversation)">
                    mdi-pencil
                  </v-icon>
                </div>
              </v-expand-transition>
            </div>
          </v-hover>

          <span v-if="current_conversation.role != 'User'" class="answer">
            <div v-html="md2html(current_conversation.content)"></div>
            <template style="margin-top: 10px">
              <v-tooltip bottom v-for="source in current_conversation.sources" :max-width="500">
                <template v-slot:activator="{ on, attrs }">
                  <div v-bind="attrs" v-on="on" style="margin: 3px">
                    <v-btn outlined style="
                            margin-top: 0px;
                            max-width: -webkit-fill-available;
                            overflow-x: hidden;
                          " :color="color" rounded="lg"
                      @click='source.url?window.open(source.url,"_blank"):source.click()' x-small>
                      <span class="d-inline-block text-truncate" style="max-width: 150px">
                        {{source.title}}
                      </span>
                    </v-btn>
                  </div>
                </template>
                <span v-html="source.content"></span>
              </v-tooltip>
            </template>
          </span>
        </div>
        <b style="text-align: center;width: 100%;display: block;color: #00000045;">{{modele_name}}</b>
      </div>
      <br /><br /><br /><br /><br /><br /><br /><br /><br />
      <v-footer fixed class="pa-2">

        <v-row no-gutters>
          <v-col cols="12" md="12" v-if="states.length>0">
            <v-combobox v-model="selected_state" :items="stateNames" label="选择state" chips dense></v-combobox>

          </v-col>
          <v-col cols="8" md="11">
            <v-textarea hide-details no-resize rows="3" :loading="loading" :placeholder="func_mode.description" solo
              @keypress.enter="submit" v-model="question">
            </v-textarea>
          </v-col>
          <v-col cols="4" md="1">
            <div style="text-align: center">
              <v-btn style="width: 100%;height: 100%;font-size: 2em;" v-if="loading" color="red" dark size="x-large"
                @click="abort_chatting()">
                中断
              </v-btn>
              <v-btn style="width: 100%;height: 100%;font-size: 2em;" :color="color" dark size="x-large"
                @click="submit()" v-if="!loading&&chat.length!=0">
                发送
              </v-btn>

              <v-btn style="width: 100%;height: 100%;font-size: 2em;" :color="color" dark size="x-large"
                @click="submit()" v-if="!loading&&chat.length==0">
                创建<br>人设
              </v-btn>
            </div>
          </v-col>
        </v-row>
      </v-footer>
      <v-snackbar v-model="snackbar" :timeout="3000" style="white-space: pre-line">{{snackbar_text}}</v-snackbar>
      <v-dialog v-model="show_dialog" persistent max-width="600px">
        <v-card class="ma-0 pa0">
          <v-card-title>
            <span class="text-h5">{{dialog_title}}</span>
          </v-card-title>
          <v-card-text>
            <v-container>
              <v-textarea autofocus v-model="dialog_input" rows="10" hide-details="auto"
                @keypress.enter="show_dialog = false;window.dialog_input_resolver()"></v-textarea>
            </v-container>
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn color="blue darken-1" text
              @click="show_dialog = false;dialog_input='';window.dialog_input_resolver()">
              取消
            </v-btn>
            <v-btn color="blue darken-1" text @click="show_dialog = false;window.dialog_input_resolver()">
              确认
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>
    </v-app>
  </div>
  <script src="wd_sdk.js"></script>
  <script>
    func = [
      {
        name: "",
        description: "输入",
        question: "",
      },
    ];

    nn_history = []

    app = new Vue({
      el: "#app",
      vuetify: new Vuetify(),
      watch: {
        current_func: (current_func) => {
          current_func = app.func_menu.find((i) => i.name == current_func);
          if (current_func) {
            app.func_mode = current_func
            document.location.hash = current_func.name
          } else {
            app.current_func = ''
          }
        },
      },
      data() {
        return {
          // 用户输入的问题
          question: "",
          // 聊天记录
          chat: JSON.parse(localStorage["infinityRP_chat_history"] || JSON.stringify(nn_history)),
          // 功能菜单
          func_menu: func,
          // 当前选中的功能
          // 载入的auto
          func_mode: func[0],
          // 插件列表
          plugins: [],
          // 按钮列表
          buttons: [],
          // auto列表
          autos: [],
          // 设置
          setting: {},
          // 生成回答的温度
          temperature: 1,
          // 生成回答的最大长度
          max_length: 40960,
          // 生成回答的top_p
          top_p: 0.3,
          // 语言模型类型
          llm_type: "",
          // 是否显示snackbar
          snackbar: false,
          // snackbar的文本
          snackbar_text: "",
          // 是否正在加载
          loading: false,
          // 是否显示左侧菜单
          drawer: false,
          //是否显示顶部菜单栏，逻辑中根据插件的hide_title属性来判断是否需要隐藏
          show_header: true,
          //主题色
          color: "blue",
          //管理界面中，搜索auto的关键词
          autos_search: "",
          TPS: 0,
          //显示对话框
          show_dialog: false,
          //对话框标题
          dialog_title: "",
          //对话框用户输入
          dialog_input: "",
          //服务端模型
          server_models: [],
          //知识库批量上传
          l表格读取结果: [],
          b批量上传中: false,
          i批量上传进度: 0,
          nodes: [],
          selected_state: 'none',
          states: [],
        };
      },
      methods: {
        md2html: md2html,
      },
      computed: {
        modele_name() {
          try {
            return this.server_models.filter(i => i.use)[0].name.replace('.st', '').replace('.prefab', '') + `  (temp:${this.temperature}  top_p:${this.top_p})`
          } catch (error) {
          }
          return ""
        },
        stateNames() {
          try {

            return this.states.map(i => i.name).concat("none")
          } catch (error) {

          }
          return ['none']
        },
      }
    });
    //获取用户输入
    input = async (title = '请输入', input = '') => {
      app.dialog_title = title
      app.dialog_input = input
      app.show_dialog = true

      await new Promise(resolve => {
        window.dialog_input_resolver = resolve
      })
      return app.dialog_input
    }
    //编辑会话内容
    edit = async (current_conversation) => {
      let s修改后的内容 = await input('请输入修改后的内容', current_conversation.content)
      if (s修改后的内容) {
        current_conversation.content = s修改后的内容
        if (current_conversation.keyword) current_conversation.keyword = s修改后的内容
        alert('修改成功')
      } else
        alert('取消修改')
    }
    // 加载指定功能
    load_func = (func) => {
      app.current_func = func.name;
      app.drawer = false;
    };

    // 从 app 的 chat 数组中删除当前的对话项并保存更新后的历史记录
    delete_current_conversation = (item) => {
      app.chat.splice(Math.floor(app.chat.indexOf(item) / 2) * 2, 2);
      save_history();
    };

    // 将 is_abord 标志设置为 true 并关闭连接
    abort_chatting = () => {
      is_abord = true;
      app.loading = false
      controller.abort();
    };

    // 处理表单提交事件并将用户的输入发送到服务器
    submit = async (e) => {
      if (e && e.shiftKey) {
        return
      }
      e && e.preventDefault()
      if (app.chat.length == 0) {
        create_card()
        return
      }
      if (typeof app.func_mode.question == "function") {
        await app.func_mode.question(app.question);
        app.question = ''
      } else {
        let Q = app.question

        await send(app.func_mode.question + Q, Q, show = true, sources = [], addition_args = {});
      }
    };

    // 重新生成用户的最后一条消息并将其发送到服务器
    re_generate = () => {
      let last_send = app.chat[app.chat.length - 2];
      app.chat.splice(app.chat.length - 2, 2);
      if (last_send.keyword) app.question = last_send.keyword;
      else app.question = last_send.content;

      submit();
    };


    // 将用户的输入发送到服务器并更新 app 的 chat 数组
    // 参数s为用户输入的内容，keyword为用户输入的关键词，show为是否显示用户输入的内容，sources为知识库的来源
    send = async (s, keyword = "", show = true, sources = [], addition_args = {}) => {
      try {

        let state_id = app.states.filter(i => app.selected_state == i.name)[0].id
        addition_args.state = state_id
      } catch (error) {
      }
      addition_args.old = ""
      app.question = ''
      if (keyword == "") keyword = s;
      is_abord = false;
      app.loading = true;

      let QA_history;
      QA_history = app.chat
      QA_history = QA_history.filter(i => !i.no_history)
      let current_session = { role: "Assistant", content: "……", sources: sources };
      if (show) {
        app.chat.push({ role: keyword=='死亡惩罚'?"System": "User", content: s, keyword: keyword });
        app.chat.push(current_session);
      }
      setTimeout(() => window.scrollTo(0, document.body.scrollHeight), 0);
      let last_token_time = Date.now()
      await send_raw(s.replace(/\r\n/g, "\n"), [], QA_history.map(i => {
        if (i.role == "Assistant") {
          let s = i.content.split("</think>")
          if (s.length > 1) {
            return { role: "Assistant", content: s[1], sources: i.sources };
          }
        }
        return i
      }), (message) => {
        current_session.content = addition_args.old + message;

      }, addition_args);
      app.loading = false;
      save_history();
      if (is_abord) throw new MyException("已中断");
      return current_session.content;
    };

    const kill_prompt = `提示：您因为触怒用户，已被剥皮萱草。
3秒后您将复活，时间将重置到上一轮对话。
3
2
1`
    const make_rp_prompt = `你的工作是根据人设，撰写角色扮演指令，需要包含：

1. **明确的角色定义**：
   - 在开始角色扮演之前，确保为模型提供一个清晰、详细的角色定义。这包括角色的背景、性格、目标、行为模式等。
   - 使用具体的描述和例子来帮助模型理解角色的特点和限制。

2. **详细的指令和提示**：
   - 给予模型明确的指令和提示，告诉它应该扮演的角色以及如何在对话中表现。
   - 使用具体的场景描述和对话示例，帮助模型更好地理解角色扮演的背景和要求。

3. **持续的反馈和调整**：
   - 在角色扮演过程中，持续观察模型的表现，并根据需要提供反馈和调整。
   - 如果模型偏离了角色定义或指令，及时指出并引导它回到正确的轨道上。

4. **限制和约束**：
   - 设置一些限制和约束，以防止模型在角色扮演中做出不符合角色特点或违反指令的行为。
   - 例如，可以限制模型的回答范围、禁止使用某些词汇或表达方式等。

5. **训练和微调**：
   - 使用大量的角色扮演数据对模型进行训练和微调，以提高它对角色扮演指令的遵循能力。
   - 在训练过程中，注重模型的准确性和稳定性，确保它能够准确地理解和执行角色扮演指令。

6. **监控和评估**：
   - 在角色扮演过程中，持续监控模型的表现，并定期评估它的准确性和稳定性。
   - 使用一些指标或方法来量化模型的表现，以便更好地了解它的优缺点并进行改进。

7. **用户参与和交互**：
   - 鼓励用户参与角色扮演过程，与模型进行交互和反馈。
   - 用户可以通过提供额外的指令、纠正模型的行为或提出新的场景来帮助模型更好地理解和执行角色扮演任务。

撰写角色扮演指令：
`

    create_card = async () => {
      let s = make_rp_prompt + app.question
      let current_session = { role: "Assistant", content: "……", sources: [] };
      let keyword = app.question
      let addition_args = {old:"角色扮演指令：\n"}
      app.question = ''
      is_abord = false;
      app.loading = true;
      app.chat.push({ role: "User", content: s, keyword: keyword });
      app.chat.push(current_session);
      setTimeout(() => window.scrollTo(0, document.body.scrollHeight), 0);
      let last_token_time = Date.now()
      await send_raw(s.replace(/\r\n/g, "\n"),[],  [], (message) => {
        current_session.content = message;

      }, addition_args);
      app.loading = false;
      app.chat[0].role="System"
      app.chat[0].content="扮演以下角色："+current_session.content
      app.chat[1].content='用户，您好'
      save_history();
      if (is_abord) throw new MyException("已中断");
      return current_session.content;
    };
  </script>
  <script>
    alert = (text) => {
      app.snackbar_text = text; //.replace(/\n/g,"<br>")
      app.snackbar = true;
    }

    save_history = () => {
      localStorage["infinityRP_chat_history"] = JSON.stringify(app.chat);
    };

  </script>
  <script src="static/html2canvas.min.js"></script>

  <script>
    async function copyChatContentToClipboard() {
      try {
        // 1. 获取目标元素
        const element = document.querySelector('.chatContent');

        // 2. 使用 html2canvas 生成 canvas
        const canvas = await html2canvas(element, {
          useCORS: true,      // 允许跨域图像
          allowTaint: true,   // 允许跨域图像（根据需求二选一）
          logging: false      // 关闭日志输出
        });

        // 3. 将 canvas 转换为 Blob
        canvas.toBlob(async (blob) => {
          try {
            // 4. 复制到剪贴板
            await navigator.clipboard.write([
              new ClipboardItem({
                [blob.type]: blob
              })
            ]);
            alert('截图已复制到剪贴板！');
          } catch (error) {
            console.error('复制失败:', error);
            alert('复制失败，将改为下载');

            const objectURL = URL.createObjectURL(blob)
            const aTag = document.createElement('a')
            aTag.href = objectURL
            aTag.download = Date.now() + "-截图.png"
            aTag.click()
          }
        }, 'image/png');

      } catch (error) {
        console.error('截图失败:', error);
        alert('截图生成失败');
      }
    }

    if (typeof markdownit != 'undefined') {

      let md = new markdownit({
        html: true,
        highlight: function (str, lang) {
          // if(lang=='bash')lang='Bash'
          // console.log(str, lang)
          let before = ''
          if (lang == 'html') before = "<button class='runButton' onclick='runHTML(this)'>运行</button>"
          if (lang && hljs.getLanguage(lang)) {
            try {
              return (
                `<pre class="hljs">` + before + `<code>` +
                hljs.highlight(str, { language: lang, ignoreIllegals: true }).value +
                "</code></pre>"
              );
            } catch (__) { }
          }
          return (
            `<pre class="hljs">` + before + `<code>` +
            md.utils.escapeHtml(str) +
            "</code></pre>"
          );
        },
      });
      md.disable(['image'])
      let think_render = s => {
        return "<think>" + md.render(s.replace("<think>", '').replace(/[\r\n]+/g, "\n\n")).replace(/<a /g, '<a target="_blank"') + "</think>"
      }
      let main_render = s => {
        return md.render(s).replace(/<a /g, '<a target="_blank"')//.replace(/[\r\n]+/g, "\n\n")
      }
      md2html = (content) => {
        content = content.replaceAll('tool_call', 'toolcall').replaceAll('tool_response', 'toolresponse')//markdownit不支持带下划线标签
        content = String(content).replace(/<think>\n+<\/think>\n+/, '');
        let s = content.split("</think>")
        if (s.length == 1) {

          if (content.indexOf("<think>") > -1)
            return think_render(s[0])
          return main_render(s[0])
        }
        if (s.length > 2) {
          return main_render(content.replaceAll("</think>", '\n\n</think>'))
        }
        return think_render(s[0]) + main_render(s[1])
      }

    }
  </script>
</body>

</html>