<html>

<head>
  <title>对话</title>
  <meta charset="utf-8" />
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <meta name="mobile-web-app-capable" content="yes" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=500,  user-scalable=no" />
  <link href="static/mdi_font/css/materialdesignicons.min.css" rel="stylesheet" />

  <link href="static/vuetify.min.css" rel="stylesheet" />
  <link href="static/highlight.min.css" rel="stylesheet" />
  <link href="common.css" rel="stylesheet" />
  <script src="static/vue.min.js"></script>
  <script src="static/vuetify.min.js"></script>
  <script src="static/markdown-it.min.js"></script>
  <script src="static/highlight.min.js"></script>
  <script src="static/math.min.js"></script>
  <style>
    .v-application p {
      margin-bottom: unset;
    }

    .v-navigation-drawer__content::-webkit-scrollbar {
      width: 5px;
    }

    .v-navigation-drawer__content::-webkit-scrollbar-track {
      background-color: #333;
    }

    /* 滚动条的滑轨背景颜色 */

    .v-navigation-drawer__content::-webkit-scrollbar-thumb {
      background-color: #aaa;
    }

    .v-navigation-drawer__content::-webkit-scrollbar-button {
      display: none;
    }


    div[aria-haspopup] {
      display: inline-block;
      margin: 10px;
    }


    .ask,
    .answer,
    .system {
      padding: 1em;
      background: #fff;
      border-radius: 16px;
      max-width: 80%;
      margin: 0 10px;
      word-break: break-word;
      line-height: 1.2;
    }


    .system {
      background: transparent;
      max-width: 100%;
      margin: 0px 0px 10px 56px;
      color: #001e67;
    }

    .ask {
      margin-left: auto;
      white-space: break-spaces;
    }

    .头像 {
      margin-bottom: auto;
      color: #fff !important;
      position: unset;
    }

    .answer img {
      max-width: 100%;
    }

    #app,
    .v-application--wrap {
      min-height: 100%;
      background: transparent;
    }

    .float {
      position: absolute !important;
    }

    .float button {
      display: block !important;
    }

    header {
      position: fixed !important;
      top: 0;
      z-index: 1;
      width: 100%;
    }


    html,
    .v-window__container,
    .chatContent {
      background-color: #f0f0f0;
    }

    .user-avatar {
      background-color: #0000a0bb;
    }

    .bot-avatar {
      background-color: #2196f3;
    }

    .v-application a {
      line-height: 1.3;
    }

    .v-input.v-textarea.v-text-field {
      padding-top: 0;
      margin-top: 0;
    }

    .v-application--is-ltr .v-input--selection-controls__input {
      /* 紧凑右上角开关 */
      margin-right: 0px;
    }

    .v-slide-group:not(.v-slide-group--has-affixes)>.v-slide-group__prev {
      /* 隐藏手机选项卡左侧空白 */
      display: none !important;
    }

    .v-sheet.v-card {
      margin: 20px;
      padding: 10px;
    }

    .chat_toolbar {
      color: #d0d0d0;
    }

    .chat_toolbar .v-input,
    .chat_toolbar div[aria-haspopup] {
      margin: 0;
    }

    footer.v-footer.pa-2.v-sheet.theme--light.v-footer--fixed {
      background-color: rgba(255, 255, 255, 0.8);
    }

    .v-snack__content {
      max-height: 40em;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space-collapse: preserve-breaks;
      text-wrap-mode: nowrap;
    }

    .v-snack__action {
      position: absolute;
      right: 0;
      top: 0;
      margin: 0 !important;
    }

    .v-snack__action>.v-snack__btn.v-btn {
      min-width: 30px;
    }

    arguments th {
      min-width: 4em;
    }
  </style>
</head>

<body>
  <div id="app" v-cloak>
    <v-app>
      <v-navigation-drawer width="350" v-model="drawer" style="height: 100%; position: fixed" temporary>

        <v-list>
          <div>

            <v-btn class="ma-3" @click="add_new_chat()" :color="color"><v-icon>mdi-pencil-plus</v-icon> 创建新对话
            </v-btn>
            <v-btn class="ma-3" @click="add_new_study_chat()" :color="color">创建知识提取对话
            </v-btn>
          </div>
          <v-divider></v-divider>
          <v-list-item v-for="chat,index in chats" @click="load_chat(chat)"
            :class="{ 'v-list-item--active': index == profiles.chat_index }">
            <v-list-item-title v-text="chat.name"> </v-list-item-title>
            <v-spacer></v-spacer>
            <v-icon @click.stop="edit_chat_name(index)">mdi-pencil</v-icon>
            <v-icon @click.stop="delete_chat(index)">mdi-delete</v-icon>
          </v-list-item>
        </v-list>
      </v-navigation-drawer>
      <v-toolbar style="opacity: 80%">
        <v-app-bar-nav-icon @click="drawer = !drawer"></v-app-bar-nav-icon>
        <v-toolbar-title style="font-size: xx-large;color: black;">{{chat_name}}</v-toolbar-title>
        <v-spacer></v-spacer>
        <div class="chat_toolbar">
          <b style="
          width: 5em;
          display: inline-block;
      ">TPS:{{TPS.toFixed(2)}}</b>
          <v-tooltip bottom>
            <template v-slot:activator="{ on, attrs }">
              <div v-bind="attrs" v-on="on">
                <v-icon :color="profiles.auto_confirm?color:'gray'"
                  @click="profiles.auto_confirm=!profiles.auto_confirm" large>
                  mdi-check-circle
                </v-icon>
              </div>
            </template>
            <span>自动确认</span>
          </v-tooltip>
          <v-tooltip bottom>
            <template v-slot:activator="{ on, attrs }">
              <div v-bind="attrs" v-on="on">
                <v-icon :color="profiles.no_COT?'red':'gray'" @click="profiles.no_COT=!profiles.no_COT" large>
                  mdi-link-off
                </v-icon>
              </div>
            </template>
            <span>跳过思考:{{profiles.no_COT?'开':'关'}}</span>
          </v-tooltip>
          <v-tooltip bottom>
            <template v-slot:activator="{ on, attrs }">
              <div v-bind="attrs" v-on="on">
                <v-icon :color="color" @click="re_generate()" large :disabled="(loading||chat.length==0)">
                  mdi-refresh
                </v-icon>
              </div>
            </template>
            <span>重试</span>
          </v-tooltip>
          <v-tooltip bottom>
            <template v-slot:activator="{ on, attrs }">
              <v-icon :color="color" v-bind="attrs" v-on="on" @click="copyChatContentToClipboard()" large
                :disabled="(loading||chat.length==0)">
                mdi-file-png-box
              </v-icon>
            </template>
            <span>截图</span>
          </v-tooltip>
        </div>
      </v-toolbar>
      <div class="chatContent" style="margin-top: 60px;">
        <div v-for="(current_conversation, index) in chat" :key="index"
          :class="['d-flex flex-row align-center my-2', current_conversation.role == 'User' ? 'justify-end': null]">
          <span v-if="current_conversation.role == 'User'" class="ask"
            v-text="current_conversation.textToDisplay.length>0?current_conversation.textToDisplay:current_conversation.content"></span>
          <v-hover v-slot="{ hover }">
            <div :style="current_conversation.role == 'System'?'display:none':''">
              <v-avatar :class="current_conversation.role == 'User' ? 'user-avatar': 'bot-avatar'" size="36" class="头像">
                <span>{{ {User:"问",Assistant:'答'}[current_conversation.role]
                  }}</span>
              </v-avatar>
              <br />
              <v-expand-transition>
                <div v-if="hover" class="float">
                  <v-icon v-if="!loading" large text :color="color"
                    @click="delete_current_conversation(current_conversation)">
                    mdi-delete
                  </v-icon>
                  <v-icon v-if="current_conversation.role!='System'" large text :color="color"
                    @click="copy(current_conversation.content)">
                    mdi-content-copy
                  </v-icon>
                  <v-icon v-if="current_conversation.role!='System'" large text :color="color"
                    @click="edit(current_conversation)">
                    mdi-pencil
                  </v-icon>
                </div>
              </v-expand-transition>
            </div>
          </v-hover>


          <span v-if="current_conversation.role == 'System'" class="system"
            v-html="md2html(current_conversation.textToDisplay)">

          </span>

          <span v-if="current_conversation.role == 'Assistant'" class="answer">
            <template v-for="(item, index) in parsedOutput(current_conversation.content)" :key="index">
              <template v-if="item.contentType === 'TOOL_CALL'">
                <toolcall v-if="item.function!='选择下步操作'">
                  <function>{{ item.function }}</function>
                  <arguments class="md" v-html="item.arguments"></arguments>
                </toolcall>
                <div v-if="item.function=='选择下步操作'">
                  请选择下步操作：
                  <template v-for="step in item.src[1].steps?.split('\n').filter(i=>i!='')">
                    <br>
                    <v-btn outlined style="
                            margin-top: 10px;
                            min-width: 20em;
                          " :color="color" @click='send(step)'>
                      {{step}}
                    </v-btn>
                  </template>
                </div>
              </template>

              <div v-else class="md" v-html="(item.content)"></div>
            </template>
            <!-- <div v-html="md2html(current_conversation.content)"></div> -->
            <template style="margin-top: 10px">
              <v-tooltip bottom v-for="source in current_conversation.sources" :max-width="500">
                <template v-slot:activator="{ on, attrs }">
                  <div v-bind="attrs" v-on="on" style="margin: 3px">
                    <v-btn outlined style="
                            margin-top: 0px;
                            max-width: -webkit-fill-available;
                            overflow-x: hidden;
                          " :color="color" rounded="lg" @click='source.click?source.click():alert(source.content)'
                      x-small>
                      <span class="d-inline-block text-truncate" style="max-width: 250px">
                        {{source.title}}
                      </span>
                    </v-btn>
                  </div>
                </template>
                <span v-html="source.content" class="" style="max-width: 30em"></span>
              </v-tooltip>
            </template>
          </span>
        </div>
      </div>
      <div style="width: 100%;text-align: center;margin: 20px;margin-bottom: 250px;">
        <v-btn v-if="chat.length>0&&!loading" @click="add_new_chat()"><v-icon>mdi-pencil-plus</v-icon> 创建新对话 </v-btn>
      </div>
      <v-footer fixed class="pa-2">
        <v-row no-gutters>
          <v-col cols="12" md="12">
            <v-autocomplete multiple clearable deletable-chips dense chips disable-lookup small-chips
              prepend-icon="mdi-toolbox" v-model="profiles.selected_tools" :items="toolsNames" hide-details label="选择工具"
              class="mt-0 ">
            </v-autocomplete>
          </v-col>
          <v-col cols="9" md="11" class="pr-2">
            <v-textarea hide-details no-resize rows="3" :loading="loading" placeholder="输入" solo
              @keypress.enter="submit" v-model="profiles.question">
            </v-textarea>
          </v-col>
          <v-col cols="3" md="1">
            <div style="text-align: center">
              <v-btn style="width: 100%;height: 100%;font-size: 2em;" v-if="loading" color="red" dark size="x-large"
                @click="abort_chatting()">
                中断
              </v-btn>
              <v-btn style="width: 100%;height: 100%;font-size: 2em;" :color="color" dark size="x-large"
                @click="submit()" v-if="!loading">
                发送
              </v-btn>

            </div>
          </v-col>
        </v-row>
      </v-footer>
      <v-snackbar v-model="snackbar" :timeout="30000" closeable>{{snackbar_text}}
        <template v-slot:action="{ attrs }">
          <v-btn color="pink" width="20" text v-bind="attrs" @click="snackbar = false"> x </v-btn>
        </template>
      </v-snackbar>
      <v-dialog v-model="show_dialog" persistent max-width="600px">
        <v-card class="ma-0 pa0">
          <v-card-title>
            <span class="text-h5">{{dialog_title}}</span>
          </v-card-title>
          <v-card-text>
            <v-container>
              <v-textarea autofocus v-model="dialog_input" rows="5" hide-details="auto"
                @keypress.enter="show_dialog = false;window.dialog_input_resolver()"></v-textarea>
            </v-container>
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn color="blue darken-1" text
              @click="show_dialog = false;dialog_input='';window.dialog_input_resolver()">
              取消
            </v-btn>
            <v-btn color="blue darken-1" text @click="show_dialog = false;window.dialog_input_resolver()">
              确认
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>
      <v-dialog v-model="show_confirm" persistent max-width="600px">
        <v-card class="ma-0 pa0">
          <v-card-title>
            <span style="max-height: 14em;overflow-y: auto;width: 100%;" v-html="md2html(confirm_title)"></span>
          </v-card-title>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn color="blue darken-1" text
              @click="show_confirm = false;confirm_input=false;window.confirm_input_resolver()">
              否
            </v-btn>
            <v-btn color="blue darken-1" text
              @click="show_confirm = false;confirm_input=true;window.confirm_input_resolver()">
              是
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>
    </v-app>
  </div>
  <script src="wd_sdk.js"></script>
  <script src="tools.js"></script>
  <script>
    watch_throttle = (func, delay) => {
      return {
        handler: throttle(func, delay),
        deep: true
      }
    };
    let chat_name = '对话'
    let name_space = 'rzyzzgjj'
    if (location.hash) {
      (function () {

        let o = decodeURIComponent(atob(location.hash.replace(/^#/, '')))
        eval(o)
      })()


    }
    app = new Vue({
      el: "#app",
      vuetify: new Vuetify(),
      watch: {
        chat: watch_throttle((val) => {
          localStorage[name_space+"_chat_history"] = JSON.stringify(val);
        }, 500),
        chats: watch_throttle(async function (val) {
          localStorage[name_space+"_chat_historys"] = JSON.stringify(val)
        }, 500),
        profiles: watch_throttle(async function (val) {
          localStorage[name_space+"_chat_profiles"] = JSON.stringify(val)
        }, 500),
      },
      data() {
        return {
          chat_name: chat_name,
          // 聊天记录
          chat: JSON.parse(localStorage[name_space+"_chat_history"] || '[]'),
          chats: JSON.parse(localStorage[name_space+"_chat_historys"] || '[{"name": "未命名对话","chat": []}]'),
          profiles: JSON.parse(localStorage[name_space+"_chat_profiles"] || JSON.stringify({
            chat_index: 0,
            // 用户输入的问题
            question: "",
            no_COT: true,
            selected_articles: [],
            selected_tools: [],
            auto_confirm: false,
          })),
          // 生成回答的温度
          temperature: 0.5,
          // 生成回答的最大长度
          max_length: 40960,
          // 生成回答的top_p
          top_p: 0.5,
          // 是否显示snackbar
          snackbar: false,
          // snackbar的文本
          snackbar_text: "",
          // 是否正在加载
          loading: false,
          // 是否显示左侧菜单
          drawer: false,
          //主题色
          color: "primary",
          TPS: 0,
          //显示对话框
          show_dialog: false,
          //对话框标题
          dialog_title: "",
          //对话框用户输入
          dialog_input: "",
          //知识库批量上传
          l表格读取结果: [],
          b批量上传中: false,
          i批量上传进度: 0,
          articles: [],
          tools: tools,

          confirm_title: "",
          confirm_input: "",
          show_confirm: false,
        };
      },
      methods: {
        md2html: createLRUCachedFn(md2html),
        save_chat: async function () {
          //保存当前对话
          for (let key in this.profiles) {
            if (this.profiles.hasOwnProperty(key)) {
              this.chats[this.profiles.chat_index][key] = this.profiles[key];
            }
          }
          this.chats[this.profiles.chat_index].chat = this.chat;
        },
        load_chat: async function (chat, need_save = true) {
          if (this.loading) {
            alert('正在加载中，请稍后再试');
            return;
          }
          need_save && this.save_chat();

          this.chat = JSON.parse(JSON.stringify(chat.chat || []));
          for (let key in this.profiles) {
            if (chat.hasOwnProperty(key) && this.profiles.hasOwnProperty(key)) {
              this.profiles[key] = chat[key];
            }
          }
          this.profiles.chat_index = this.chats.indexOf(chat);
          setTimeout(() => window.scrollTo(0, document.body.scrollHeight), 0);
        },
        add_new_chat: async function () {
          this.chats.unshift({ name: '未命名对话', chat: [] });
          this.profiles.chat_index += 1
          this.load_chat(this.chats[0]);
        },
        add_new_study_chat: async function () {
          this.chats.unshift({
            name: '知识提取', chat: [{
              "role": "System", "content": `你的工作是从用户输入提取知识点，并使用工具保存。
不要向用户确认，直接调用工具。
文件名格式：一级目录（应当为资料学科类型）/知识点的标题.txt。
内容：不超过1000字，过长时应当分割为多个知识点。`, textToDisplay: "知识提取模式"
            },
            { "role": "Assistant", "content": `你好，请直接将文字粘贴进入输入框，不需要做格式整理和分点，我会自动进行` },
            ]
          });
          this.profiles.chat_index += 1
          this.load_chat(this.chats[0])
          this.profiles.selected_tools = ['列出文件', '编辑文件']
        },
        delete_chat: async function (index) {
          if (this.loading) {
            alert('正在加载中，请稍后再试');
            return;
          }
          if (!await confirm("确定要删除这个聊天会话吗？"))
            return
          if (this.chats.length == 1) {
            // 如果这是最后一个聊天会话，创建一个新的空会话
            this.chats = [{ name: "未命名对话", chat: [] }];
            this.profiles.chat_index = 0;
            this.chat = [];
            return
          }
          this.save_chat();
          let new_chat_index = this.profiles.chat_index;
          if (index <= new_chat_index) {
            new_chat_index--;
            if (new_chat_index < 0) {
              new_chat_index = 0;
            }
          }

          this.chats.splice(index, 1);
          this.load_chat(this.chats[new_chat_index], false);

        },
        edit_chat_name: async function (index) {
          // 获取当前聊天会话的名称
          let currentName = this.chats[index].name;
          // 获取用户输入的新名称
          let newName = await input('请输入新的对话名称', currentName);
          // 如果用户输入了新名称且不为空，则更新聊天会话名称
          if (newName && newName.trim() !== '') {
            this.chats[index].name = newName.trim();
          }
        },

        // 重新生成最后一条消息
        re_generate: async function () {
          let flag = true
          let last_send
          while (flag) {

            last_send = this.chat[app.chat.length - 2];
            this.chat.splice(this.chat.length - 2, 2);
            flag = last_send.role == 'System'
          }
          await send(last_send.content, last_send.textToDisplay)
        },

        parsedOutput: createLRUCachedFn(function (input) {
          if (!input) return [];
          function splitByXmlToolsTagSimple(input) {
            const parts = input.split(/(<tool_call[^>]*>(?:[\s\S]*?<\/tool_call>|[\s\S]*))/);
            return parts.filter(part => part.trim().length > 0);
          }
          nodes = splitByXmlToolsTagSimple(input)
          const result = [];
          nodes.forEach(node => {
            if (node.indexOf("<tool_call") == 0) {

              let parser = new DOMParser();
              node = parser.parseFromString(node, "text/html").querySelector('tool_call')
              const function_name = node?.querySelector('name')?.innerHTML || '工具名识别失败！';
              let arguments = {}
              node.querySelector('arguments')?.childNodes.forEach(i => {
                if (i.nodeName == "#text") return
                arguments[i.tagName?.toLowerCase()] = i.innerHTML
              })
              result.push({
                contentType: 'TOOL_CALL',
                function: tools.find(i => i.name == function_name)?.display_name || function_name,
                arguments: createDescriptionHtml(arguments),
                src: [function_name, arguments]
              });
            } else {

              node = this.md2html(node)
              result.push({
                contentType: 'text',
                content: node
              });
            }
          });

          return result;
        })
      },
      computed: {
        toolsNames() {
          try {
            let toolsNames = this.tools.map(i => i.display_name).filter(i => i.length > 0)
            return toolsNames
          } catch (error) {

          }
          return []
        },
        articlesNames() {
          try {
            let articlesNames = this.articles.map(i => i.title).filter(i => i.length > 0)
            return articlesNames
          } catch (error) {

          }
          return []
        },
      }
    });
    load_articles = async () => {
      articlesStorage = new clyDBStorage('articles');
      let articles = await articlesStorage["main"]
      articles = articles ? JSON.parse(articles) : init_articles
      app.articles = articles
      app.profiles.selected_articles = app.articles.filter(i => i.use).filter(i => i.title).map(i => i.title)
    }
    load_articles()
    //获取用户输入
    input = async (title = '请输入', input = '') => {
      app.dialog_title = title
      app.dialog_input = input
      app.show_dialog = true

      await new Promise(resolve => {
        window.dialog_input_resolver = resolve
      })
      return app.dialog_input
    }
    confirm = async (title = '请输入') => {
      if (app.profiles.auto_confirm) return true
      app.confirm_title = title
      app.confirm_input = input
      app.show_confirm = true

      await new Promise(resolve => {
        window.confirm_input_resolver = resolve
      })
      return app.confirm_input
    }
    //编辑会话内容
    edit = async (current_conversation) => {
      if (current_conversation.role == "User") {
        let s修改后的内容 = await input('请输入修改后的内容', current_conversation.textToDisplay)
        if (s修改后的内容) {
          current_conversation.content = s修改后的内容
          current_conversation.textToDisplay = s修改后的内容
          alert('修改成功')
        } else
          alert('取消')


      } else {

        let s修改后的内容 = await input('请输入修改后的内容', current_conversation.content)
        if (s修改后的内容) {
          current_conversation.content = s修改后的内容
          alert('修改成功')
        } else
          alert('取消')
      }
    }
    // 从 app 的 chat 数组中删除当前的对话项并保存更新后的历史记录
    delete_current_conversation = (item) => {
      app.chat.splice(Math.floor(app.chat.indexOf(item) / 2) * 2, 2);
    };

    // 将 is_abord 标志设置为 true 并关闭连接
    abort_chatting = () => {
      is_abord = true;
      app.loading = false
      controller.abort();
    };

    // 处理表单提交事件并将用户的输入发送到服务器
    submit = async (e) => {
      if (e && e.shiftKey) {
        return
      }
      e && e.preventDefault()
      if (app.loading) {
        alert("请等待对话完成")
        return
      }
      let q = app.profiles.question
      app.profiles.question = ''
      await send(q);

    };


    send = async (s, textToDisplay = false, role = "User", sources = [], addition_args = {}) => {
      if (textToDisplay == false) textToDisplay = s
      addition_args.old = app.profiles.no_COT ? templates.skip_think_prompt : ""
      is_abord = false;
      app.loading = true;

      let history = app.chat.filter(i => !i.no_history)
      let userConversation = { role: role, content: s, textToDisplay: textToDisplay }
      let assistantConversation = { role: "Assistant", content: "……", sources: sources };
      if (app.chat.length == 0 && app.chats[app.profiles.chat_index].name == '未命名对话') {
        app.chats[app.profiles.chat_index].name = s.length > 20 ? s.substring(0, 18) + '...' : s;
      }
      app.chat.push(userConversation);
      app.chat.push(assistantConversation);
      setTimeout(() => window.scrollTo(0, document.body.scrollHeight), 0);

      await send_raw(userConversation.content, app.tools.filter(i => (app.profiles.selected_tools.findIndex(s => s == i.group || s == i.display_name) > -1)), history.map(i => {
        if (i.role == "Assistant") {
          let s = i.content.split("</think>")
          if (s.length > 1) {
            return { role: "Assistant", content: s[1], sources: i.sources };
          }
        }
        return i
      }), (message) => {
        assistantConversation.content = addition_args.old + message;

      }, addition_args, role = role);
      app.loading = false;
      if (is_abord) throw new MyException("已中断");
      let parser = new DOMParser();
      let xmlDoc = parser.parseFromString(assistantConversation.content, "text/html");
      let tool_calls = xmlDoc.querySelectorAll("tool_call")
      if (tool_calls.length > 0) {
        let tool_call_results = []
        for (const tool_call of tool_calls) {
          console.log(tool_call)
          window.tool_call = tool_call
          let name = tool_call.querySelector("name").innerHTML
          let arguments_node = tool_call.querySelector("arguments")
          let arguments = (argument) => {
            return arguments_node.querySelector(argument).innerHTML
          }
          let arguments_p = new Proxy(arguments, {
            get: (target, prop) => {
              if (typeof target[prop] === 'function' || prop in target) {
                return target[prop];
              }
              return target(prop)
            }
          })
          if (name == "select_next_step") return assistantConversation.content;
          let result = await window[name](arguments_p)
          tool_call_results.push(result)
          assistantConversation.sources = [...assistantConversation.sources, ...result.sources]
        }

        let send_result = tool_call_results.map(i => `Response:${i.content}`).join('\n\n')
        return send(send_result, tool_call_results[0].text, role = "System")
      }
      return assistantConversation.content;
    };
    alert = (text) => {
      app.snackbar_text = text; //.replace(/\n/g,"<br>")
      app.snackbar = true;
    }
    function createDescriptionHtml(item) {
      let contentHtml = '';
      for (let pro in item) {
        let content
        if (Array.isArray(item[pro]))
          content = item[pro].join("<br>")
        else {
          // console.log([pro, item[pro]])
          switch (pro) {
            case "evaluate":
              content = item[pro]
              break;
            case "names":
              content = item[pro].trim().replaceAll('\n', "<br>")
              break;

            default:
              content = md2html(item[pro])
              break;
          }
        }
        contentHtml += '<tr><th>' + `${argument_name_dict[pro] || pro}` + '</th>' + '<td>' + content + '</td>' + '</tr>'
      }
      if (contentHtml == "") return ""
      return '<table><tbody>' + contentHtml + '</tbody></table>'
    }

  </script>
  <script src="static/html2canvas.min.js"></script>
  <script>
    async function copyChatContentToClipboard() {
      try {
        const element = document.querySelector('.chatContent');
        const canvas = await html2canvas(element, {
          useCORS: true,      // 允许跨域图像
          allowTaint: true,   // 允许跨域图像（根据需求二选一）
          logging: false      // 关闭日志输出
        });
        canvas.toBlob(async (blob) => {
          try {
            await navigator.clipboard.write([
              new ClipboardItem({
                [blob.type]: blob
              })
            ]);
            alert('截图已复制到剪贴板！');
          } catch (error) {
            console.error('复制失败:', error);
            alert('复制失败，将改为下载');
            const objectURL = URL.createObjectURL(blob)
            const aTag = document.createElement('a')
            aTag.href = objectURL
            aTag.download = Date.now() + "-截图.png"
            aTag.click()
          }
        }, 'image/png');
      } catch (error) {
        console.error('截图失败:', error);
        alert('截图生成失败');
      }
    }

  </script>
</body>

</html>