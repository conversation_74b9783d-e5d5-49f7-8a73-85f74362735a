<!DOCTYPE html>
<html>

<head>
  <title>闻达</title>
  <meta charset="utf-8">
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <meta name="mobile-web-app-capable" content="yes" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=700,  user-scalable=no">
  <link rel="shortcut icon" href="favicon.png" />
  <link href="static/mdi_font/css/materialdesignicons.min.css" rel="stylesheet">
  <link rel="manifest" href="manifest.json" />
  <link href="static/3/vuetify.min.css" rel="stylesheet">
  <script src="static/3/vue.global.prod.js"></script>
  <script src="static/3/vuetify.min.js"></script>
  <style>
    html,
    body,
    #app {
      overflow: hidden;
      height: 100%;
    }

    header.v-sheet.theme--dark.v-toolbar.primary {
      height: 64px !important;
    }

    .v-application--wrap {
      display: unset;
      user-select: none;
    }

    [v-cloak] {
      display: none;
    }

    .v-slide-group__content .v-btn__content {
      width: 11em;
    }

    .v-tabs--vertical>.v-tabs-bar {
      height: 100%;
    }

    .v-tabs {
      width: 200px;
      position: absolute;
      display: unset;
      height: calc(100% - 64px) !important;
      top: 64px;
    }


    iframe {
      border: 0;
      left: 200px;
      position: absolute;
      width: calc(100% - 200px);
      height: calc(100% - 64px);
      top: 64px;
    }


    .export_btn {
      margin: 0 !important;
      opacity: 0;
    }

    .export_btn:hover {
      opacity: 1 !important;
    }
  </style>
</head>

<body>
  <div id="app" v-cloak>
    <v-app>

      <v-toolbar color="#1566b7">
        <v-toolbar-title><b style="font-size: xx-large;line-height: 3em;">闻达</b></v-toolbar-title>
      </v-toolbar>

      <v-tabs direction="vertical" v-model="tab" bg-color="#1566b7" color="while">
        <v-tab v-for="(item, index) in pages.concat(zdrw_pages)" :prepend-icon="'mdi-'+item.icon" base-color="#fffa">
          {{item.title}}<v-spacer></v-spacer>
          <v-icon v-if="index<pages.length" text class="export_btn" @click="open(item.src)">mdi-export-variant
          </v-icon>
          <v-icon v-if="index>=pages.length" text class="export_btn" @click="del(index-pages.length)">mdi-delete-outline
          </v-icon>

        </v-tab>
      </v-tabs>

      <v-slide-x-reverse-transition v-for="(item, index) in pages.concat(zdrw_pages)">
        <iframe :src="item.src" v-show="tab==index" v-if="lazy_load(index)"></iframe>
      </v-slide-x-reverse-transition>
    </v-app>
  </div>
  <script>
    get_tab = () => localStorage["rzyzzgjj_tab"] == undefined ? 0 : 1 * localStorage["rzyzzgjj_tab"]
    lazys = [get_tab()]
    pages = [
      { src: 'chat.html', title: '对话', icon: 'chat-processing' },
      { src: 'zlgl.html', title: '资料管理', icon: 'folder-arrow-left-right-outline' },
      { src: 'fy.html', title: '翻译', icon: 'translate' },
      { src: 'write.html', title: '续写', icon: 'file-document-edit' },
      { src: 'yjgj.html', title: '研究工具', icon: 'thought-bubble' },
      { src: 'infinityRP.html', title: '角色扮演', icon: 'account-edit' },
      { src: 'chatCreater.html', title: '对话定制', icon: 'chat-plus' },
      { src: 'settings.html', title: '设置', icon: 'cog' },
    ]
    // if (document.location.href.indexOf('127.0.0.1') == -1 && document.location.protocol == "http:") {
    //   pages.splice(12, 1)
    // }

    const vuetify = Vuetify.createVuetify({
      defaults: {
        VBtn: {
          color: 'primary',
          variant: 'tonal'
        },
        VCheckbox: {
          color: 'primary',
          density: 'compact'
        },
      }
    })
    window.app = app = Vue.createApp({
      data: () => {
        let data
        data = {
          tab: get_tab(),
          pages: pages,
          zdrw_pages: JSON.parse(localStorage["rzyzzgjj_zdrw"] || "[]"),
        }

        return data
      },
      watch: {
        tab: tab => {
          localStorage["rzyzzgjj_tab"] = tab
          if (!(lazys.find(i => i == tab)))
            lazys.push(tab)
        }
      },
      methods: {
        lazy_load: tab => lazys.find(i => i == tab) > -1,
        open(s){window.open(s)},
        del: (i) => {

          if (confirm("确认删除第" + (i + 1) + "个保存任务？\n任务名称：" + app.zdrw_pages[i].title)) {
            app.zdrw_pages.splice(i, 1)
            localStorage["rzyzzgjj_zdrw"] = JSON.stringify(app.zdrw_pages)
          }
        }
      }
    }).use(vuetify).mount('#app')

  </script>

</body>

</html>