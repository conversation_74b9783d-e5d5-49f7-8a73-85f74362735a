<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8" />
  <title>闻达3 · 使用说明</title>
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
  <link href="static/mdi_font/css/materialdesignicons.min.css" rel="stylesheet" />
  <link href="static/vuetify.min.css" rel="stylesheet" />
  <script src="static/vue.min.js"></script>
  <script src="static/vuetify.min.js"></script>
  <style>
    html, body, #app { height: 100%; }
    [v-cloak] { display: none; }
    .hero {
      background: linear-gradient(135deg, #1566b7, #27a0ff);
      color: #fff;
      border-radius: 16px;
      padding: 36px 28px;
    }
    .container { max-width: 1080px; margin: 16px auto; }
    .mono { font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; }
    .section-card { border-radius: 14px; }
    .toc .v-list-item { border-radius: 10px; }
    .tip { color:#666; font-size:12px; }
    .kbd { font-family: ui-monospace, Menlo, Consolas, monospace; background:#eef3ff; padding:0 6px; border-radius:4px; }
  </style>
</head>
<body>
<div id="app" v-cloak>
  <v-app>
    <v-app-bar color="#1566b7" dark flat>
      <v-toolbar-title>
        <v-icon left>mdi-book-open-page-variant</v-icon>
        闻达3 · 使用说明
      </v-toolbar-title>
      <v-spacer></v-spacer>
      <v-btn text href="index.html" target="_self"><v-icon left>mdi-home</v-icon>返回首页</v-btn>
    </v-app-bar>

    <v-container class="container">
      <v-row>
        <v-col cols="12">
          <v-sheet class="hero" elevation="2">
            <div style="font-size:28px; font-weight:700; letter-spacing: .5px;">轻量本地/远程 LLM 工具集</div>
            <div style="opacity:.95; margin-top:8px; line-height:1.6;">
              支持对话、翻译、续写、研究工具、角色扮演、对话定制与资料管理；可连接本地 Llama 风格或 OpenAI 兼容后端，提供一键连通性测试与全局生成参数配置。
            </div>
            <div class="tip" style="margin-top:10px;">建议使用最新版 Chrome/Edge；首次使用请先前往“设置”配置后端与模型。</div>
          </v-sheet>
        </v-col>
      </v-row>

      <v-row>
        <v-col cols="12" md="4">
          <v-card class="section-card" outlined>
            <v-card-title>
              <v-icon class="mr-2">mdi-format-list-bulleted</v-icon>目录
            </v-card-title>
            <v-divider></v-divider>
            <v-list dense class="toc">
              <v-list-item @click="scrollTo('#intro')"><v-list-item-title>1. 前言简介</v-list-item-title></v-list-item>
              <v-list-item @click="scrollTo('#deploy')"><v-list-item-title>2. 系统部署</v-list-item-title></v-list-item>
              <v-list-item @click="scrollTo('#features')"><v-list-item-title>3. 功能说明</v-list-item-title></v-list-item>
              <v-list-item @click="scrollTo('#faq')"><v-list-item-title>4. 常见问题</v-list-item-title></v-list-item>
            </v-list>
            <v-divider></v-divider>
            <v-card-text class="tip">
              小贴士：若使用云端 OpenAI 兼容服务，建议通过反向代理注入密钥，减少前端暴露。
            </v-card-text>
          </v-card>
        </v-col>

        <v-col cols="12" md="8">
          <!-- 前言简介 -->
          <v-card id="intro" class="section-card mb-6" outlined>
            <v-card-title><v-icon class="mr-2">mdi-information-outline</v-icon>1. 前言简介</v-card-title>
            <v-card-text>
              <p>“闻达”是基于 Vue + Vuetify 构建的前端工具集，聚焦“好上手、低门槛、即开即用”的本地/远程 LLM 使用体验。其前端可直接配置模型后端（llama/openai 协议），提供对话、翻译、续写、研究工具、角色扮演、对话定制与资料管理等模块。</p>
              <ul>
                <li>轻部署：静态页面即可运行；也可配合桌面打包（Win 包等）。</li>
                <li>灵活后端：本地 Llama 风格或 OpenAI 兼容协议，自动/手动选择。</li>
                <li>本地存储：会话、资料与偏好存储在浏览器本地。</li>
                <li>体验友好：统一的设置页、温度/Top-p/长度默认值、连通性测试。</li>
              </ul>
            </v-card-text>
          </v-card>

          <!-- 系统部署 -->
          <v-card id="deploy" class="section-card mb-6" outlined>
            <v-card-title><v-icon class="mr-2">mdi-rocket-launch-outline</v-icon>2. 系统部署</v-card-title>
            <v-card-text>
              <v-alert type="info" prominent dense border="left" class="mb-4">
                推荐先准备好后端接口，再打开本页面“设置”填写 <span class="kbd">llm_server</span>、<span class="kbd">llm_protocol</span>、<span class="kbd">llm_model</span> 与可选 <span class="kbd">llm_headers</span>，点击“连通性测试”。
              </v-alert>
              <h4>2.1 后端准备</h4>
              <ol>
                <li><b>本地 Llama 风格</b>：使用 llama.cpp/ollama 或兼容服务，加载 gguf 模型并开放 HTTP 接口（如 <span class="kbd">http://127.0.0.1:8080/completion</span>）。</li>
                <li><b>OpenAI 兼容</b>：使用官方或兼容云服务，准备 <span class="kbd">/v1/chat/completions</span> 端点及 API Key。</li>
              </ol>
              <div class="tip">实测参考：Qwen3-30B A3B 量化 Q5_K_S 约需 20GB 可用内存；核显环境可选 Vulkan 后端以利用 iGPU。</div>
              <h4 class="mt-4">2.2 首次设置</h4>
              <ol>
                <li>访问首页 <span class="kbd">index.html</span> → 打开“设置”。</li>
                <li>填写 <span class="kbd">API Endpoint (llm_server)</span>，协议 <span class="kbd">llm_protocol</span> 选 <b>auto</b>/<b>llama</b>/<b>openai</b>。</li>
                <li>如需直连云端，<span class="kbd">llm_headers</span> 中填入 <span class="kbd">{"Authorization":"Bearer xxx"}</span>。</li>
                <li>设置默认模型名 <span class="kbd">llm_model</span>（如 <span class="kbd">Qwen3-...</span>）。</li>
                <li>点击“连通性测试”，成功后“保存”。</li>
              </ol>
              <h4 class="mt-4">2.3 运行建议</h4>
              <ul>
                <li>优先通过本地静态服务器访问（避免直接 file:// 导致相对路径或 CORS 问题）。</li>
                <li>安装路径尽量使用英文与无空格目录，避免罕见环境问题。</li>
              </ul>
            </v-card-text>
          </v-card>

          <!-- 功能说明 -->
          <v-card id="features" class="section-card mb-6" outlined>
            <v-card-title><v-icon class="mr-2">mdi-apps</v-icon>3. 功能说明</v-card-title>
            <v-card-text>
              <v-expansion-panels multiple>
                <v-expansion-panel>
                  <v-expansion-panel-header><v-icon left>mdi-chat-processing</v-icon>对话（chat.html）</v-expansion-panel-header>
                  <v-expansion-panel-content>
                    多轮对话、工具选择、参数可调，支持“中断/重新生成/多会话保存”。适合日常问答、头脑风暴、总结归纳。
                  </v-expansion-panel-content>
                </v-expansion-panel>
                <v-expansion-panel>
                  <v-expansion-panel-header><v-icon left>mdi-folder-arrow-left-right-outline</v-icon>资料管理（zlgl.html）</v-expansion-panel-header>
                  <v-expansion-panel-content>
                    本地管理资料（articles），配合模型进行摘要提炼或重组，支持增删改与本地持久化存储。
                  </v-expansion-panel-content>
                </v-expansion-panel>
                <v-expansion-panel>
                  <v-expansion-panel-header><v-icon left>mdi-translate</v-icon>翻译（fy.html）</v-expansion-panel-header>
                  <v-expansion-panel-content>
                    逐段翻译与术语表驱动的统一译法（l翻译规则）。建议先维护术语→再执行翻译，确保一致性与可控性。
                  </v-expansion-panel-content>
                </v-expansion-panel>
                <v-expansion-panel>
                  <v-expansion-panel-header><v-icon left>mdi-file-document-edit</v-icon>续写（write.html）</v-expansion-panel-header>
                  <v-expansion-panel-content>
                    依据已有片段进行续写/扩写，调节温度与长度，多次生成对比选优，适合内容延展与润色。
                  </v-expansion-panel-content>
                </v-expansion-panel>
                <v-expansion-panel>
                  <v-expansion-panel-header><v-icon left>mdi-thought-bubble</v-icon>研究工具（yjgj.html）</v-expansion-panel-header>
                  <v-expansion-panel-content>
                    整合摘要、提纲、对比等研究工作流，帮助快速形成结构化输出，具体动作以页面按钮为准。
                  </v-expansion-panel-content>
                </v-expansion-panel>
                <v-expansion-panel>
                  <v-expansion-panel-header><v-icon left>mdi-account-edit</v-icon>角色扮演（infinityRP.html）</v-expansion-panel-header>
                  <v-expansion-panel-content>
                    预设角色/插件/auto 列表，快速切换不同风格或场景模拟，适合语气风格模仿、专家访谈式问答等。
                  </v-expansion-panel-content>
                </v-expansion-panel>
                <v-expansion-panel>
                  <v-expansion-panel-header><v-icon left>mdi-chat-plus</v-icon>对话定制（chatCreater.html）</v-expansion-panel-header>
                  <v-expansion-panel-content>
                    将系统提示、角色卡、工具选择等组合为模板，一键复用团队常用对话场景，降低重复配置成本。
                  </v-expansion-panel-content>
                </v-expansion-panel>
                <v-expansion-panel>
                  <v-expansion-panel-header><v-icon left>mdi-cog</v-icon>设置（settings.html）</v-expansion-panel-header>
                  <v-expansion-panel-content>
                    配置 <span class="kbd">llm_server</span>/<span class="kbd">llm_protocol</span>/<span class="kbd">llm_model</span>/<span class="kbd">llm_headers</span>，并提供“连通性测试”与全局生成参数（temperature/top_p/max_length）。
                  </v-expansion-panel-content>
                </v-expansion-panel>
              </v-expansion-panels>
            </v-card-text>
          </v-card>

          <!-- 常见问题 -->
          <v-card id="faq" class="section-card mb-6" outlined>
            <v-card-title><v-icon class="mr-2">mdi-help-circle-outline</v-icon>4. 常见问题</v-card-title>
            <v-card-text>
              <v-expansion-panels multiple>
                <v-expansion-panel>
                  <v-expansion-panel-header>点击无反应？</v-expansion-panel-header>
                  <v-expansion-panel-content>
                    请优先在“设置”执行连通性测试；确认后端可用、端点正确、模型已加载。路径含非 ASCII 字符在个别环境可能致问题，建议使用纯英文目录。
                  </v-expansion-panel-content>
                </v-expansion-panel>
                <v-expansion-panel>
                  <v-expansion-panel-header>连通性测试失败？</v-expansion-panel-header>
                  <v-expansion-panel-content>
                    检查 <span class="kbd">llm_server</span> 是否可达、<span class="kbd">llm_protocol</span> 是否匹配、云端是否需要 <span class="kbd">Authorization</span> 请求头；必要时通过同源反向代理转发以避免 CORS。
                  </v-expansion-panel-content>
                </v-expansion-panel>
                <v-expansion-panel>
                  <v-expansion-panel-header>模型/内存如何选择？</v-expansion-panel-header>
                  <v-expansion-panel-content>
                    资源有限时选择更小模型或更高量化（Q4/Q5）；30B 量化约需 20GB 可用内存以保证流畅；如可用，后端选择 Vulkan 以利用核显。
                  </v-expansion-panel-content>
                </v-expansion-panel>
                <v-expansion-panel>
                  <v-expansion-panel-header>如何重置设置或清空历史？</v-expansion-panel-header>
                  <v-expansion-panel-content>
                    设置页点击“恢复默认”；或清空浏览器 localStorage（注意会丢失会话与资料）。
                  </v-expansion-panel-content>
                </v-expansion-panel>
              </v-expansion-panels>
            </v-card-text>
          </v-card>

          <div class="text-center mb-10">
            <v-btn color="primary" large href="index.html" target="_self">
              <v-icon left>mdi-home</v-icon> 返回首页
            </v-btn>
          </div>
        </v-col>
      </v-row>
    </v-container>
  </v-app>
</div>

<script>
  new Vue({
    el: '#app',
    vuetify: new Vuetify(),
    methods: {
      scrollTo(sel){
        const el = document.querySelector(sel);
        if(!el) return;
        window.scrollTo({ top: el.getBoundingClientRect().top + window.pageYOffset - 72, behavior: 'smooth' });
      }
    }
  })
</script>
</body>
</html>

